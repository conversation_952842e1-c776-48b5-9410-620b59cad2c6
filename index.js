require("dotenv").config();
const AWSManager = require('./aws-manager');

// Configuration constants
const CONFIG = {
  TOTAL_BUCKETS: 10,
  BUCKET_PREFIX: 'test-bucket',
  REGION: 'us-east-1',
  ORIGIN_LIMIT: 25,
  RANDOM_REGIONS: true
};

// Initialize AWS Manager
const awsManager = new AWSManager(CONFIG);

// Example usage - you can uncomment these to test different functions
(async () => {
  try {
    // Create 10 buckets with random regions
    console.log('🚀 Creating 10 buckets with random regions...\n');
    await awsManager.createBuckets(true);

    // Uncomment below to delete buckets containing 'test'
    // await awsManager.deleteBucketsContaining('test');

    // Uncomment below to map buckets to distributions
    // await awsManager.mapBucketsToDistributions();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
})();
