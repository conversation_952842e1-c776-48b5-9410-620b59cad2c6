require("dotenv").config();
const AWSManager = require('./aws-manager');

// Configuration constants
const CONFIG = {
  TOTAL_BUCKETS: 5,
  BUCKET_PREFIX: 'test-bucket',
  REGION: 'us-east-1',
  ORIGIN_LIMIT: 25
};

// Initialize AWS Manager
const awsManager = new AWSManager(CONFIG);

// Example usage - you can uncomment these to test different functions
(async () => {
  try {
    // await awsManager.mapBucketsToDistributions();

    // Uncomment below to delete buckets containing a specific string
    // First do a dry run to see what would be deleted
    await awsManager.deleteBucketsContaining('test');

    // Then actually delete them (remove the 'true' at the end or set it to 'false')
    // await awsManager.deleteBucketsContaining('test-bucket', true, false);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
})();
