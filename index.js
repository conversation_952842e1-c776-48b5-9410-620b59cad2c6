require("dotenv").config();
const AWSManager = require('./aws-manager');

// Configuration constants
const CONFIG = {
  TOTAL_BUCKETS: 5,
  BUCKET_PREFIX: 'test-bucket',
  REGION: 'us-east-1',
  ORIGIN_LIMIT: 25
};

// Initialize AWS Manager
const awsManager = new AWSManager(CONFIG);

// Example usage - you can uncomment these to test different functions
(async () => {
  try {
    // Map buckets to distributions
    await awsManager.mapBucketsToDistributions();

    // Uncomment below to create buckets and distribution
    // await awsManager.createBuckets();
    // await awsManager.createCloudFrontDistribution();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
})();
