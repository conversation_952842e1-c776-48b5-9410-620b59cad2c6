const {
  CloudFrontClient,
  ListDistributionsCommand,
  CreateDistributionCommand,
} = require("@aws-sdk/client-cloudfront");
const {
  S3Client,
  ListBucketsCommand,
  CreateBucketCommand,
} = require("@aws-sdk/client-s3");

/**
 * AWS Manager class for handling S3 and CloudFront operations
 */
class AWSManager {
  constructor(config = {}) {
    this.config = {
      TOTAL_BUCKETS: config.TOTAL_BUCKETS || 5,
      BUCKET_PREFIX: config.BUCKET_PREFIX || 'test-bucket',
      REGION: config.REGION || 'us-east-1',
      ORIGIN_LIMIT: config.ORIGIN_LIMIT || 25,
      ...config
    };

    this.credentials = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    };

    // Initialize AWS clients
    this.s3 = new S3Client({ 
      region: this.config.REGION, 
      credentials: this.credentials 
    });
    
    this.cloudfront = new CloudFrontClient({
      region: "us-east-1", // CloudFront is global, but use us-east-1
      credentials: this.credentials
    });
  }

  /**
   * Create multiple S3 buckets based on configuration
   */
  async createBuckets() {
    console.log("🚀 Creating buckets...");
    
    for (let i = 1; i <= this.config.TOTAL_BUCKETS; i++) {
      const bucketName = `${this.config.BUCKET_PREFIX}-${i.toString().padStart(3, "0")}`;
      try {
        await this.s3.send(
          new CreateBucketCommand({
            Bucket: bucketName,
            ...(this.config.REGION !== "us-east-1"
              ? { CreateBucketConfiguration: { LocationConstraint: this.config.REGION } }
              : {})
          })
        );
        console.log(`✅ Created bucket: ${bucketName}`);
      } catch (err) {
        if (err.name === "BucketAlreadyOwnedByYou") {
          console.log(`⚠️  Bucket already exists: ${bucketName}`);
        } else {
          console.error(`❌ Error creating ${bucketName}: ${err.message}`);
        }
      }
    }
  }

  /**
   * Create CloudFront distribution with S3 origins
   * @param {Array} bucketNames - Optional array of bucket names, if not provided will generate from config
   */
  async createCloudFrontDistribution(bucketNames = null) {
    console.log("🚀 Creating CloudFront distribution...");
    
    if (!bucketNames) {
      bucketNames = Array.from({ length: this.config.TOTAL_BUCKETS }, (_, i) =>
        `${this.config.BUCKET_PREFIX}-${(i + 1).toString().padStart(3, "0")}`
      );
    }

    const origins = bucketNames.slice(0, this.config.ORIGIN_LIMIT).map((bucket, index) => ({
      Id: `Origin${index + 1}`,
      DomainName: `${bucket}.s3.amazonaws.com`,
      S3OriginConfig: {
        OriginAccessIdentity: ""
      }
    }));

    const distributionParams = {
      DistributionConfig: {
        CallerReference: `${Date.now()}`,
        Comment: "Test distribution with multiple S3 origins",
        Enabled: true,
        Origins: {
          Quantity: origins.length,
          Items: origins
        },
        DefaultCacheBehavior: {
          TargetOriginId: "Origin1",
          ViewerProtocolPolicy: "redirect-to-https",
          ForwardedValues: {
            QueryString: false,
            Cookies: { Forward: "none" }
          },
          TrustedSigners: {
            Enabled: false,
            Quantity: 0
          },
          MinTTL: 0
        }
      }
    };

    try {
      const result = await this.cloudfront.send(
        new CreateDistributionCommand(distributionParams)
      );
      console.log("✅ CloudFront Distribution ID:", result.Distribution.Id);
      console.log("🌍 Domain Name:", result.Distribution.DomainName);
      return result.Distribution;
    } catch (err) {
      console.error("❌ Failed to create CloudFront distribution:", err.message);
      throw err;
    }
  }

  /**
   * List all S3 buckets
   * @returns {Array} Array of bucket names
   */
  async listAllBuckets() {
    try {
      const res = await this.s3.send(new ListBucketsCommand({}));
      return res.Buckets.map(b => b.Name);
    } catch (err) {
      console.error("❌ Failed to list buckets:", err.message);
      return [];
    }
  }

  /**
   * Get all CloudFront distributions
   * @returns {Array} Array of distribution objects
   */
  async getAllDistributions() {
    const all = [];
    let Marker;

    try {
      do {
        const res = await this.cloudfront.send(new ListDistributionsCommand({ Marker }));
        const dists = res.DistributionList?.Items || [];
        all.push(...dists);
        Marker = res.DistributionList?.NextMarker;
      } while (Marker);
    } catch (err) {
      console.error("❌ Failed to list distributions:", err.message);
    }

    return all;
  }

  /**
   * Map S3 buckets to their associated CloudFront distributions
   */
  async mapBucketsToDistributions() {
    const buckets = await this.listAllBuckets();
    const distributions = await this.getAllDistributions();

    const bucketToDists = {}; // { bucketName: [distId, ...] }

    for (const bucket of buckets) {
      bucketToDists[bucket] = [];
    }

    for (const dist of distributions) {
      const distId = dist.Id;
      const origins = dist.Origins?.Items || [];

      for (const origin of origins) {
        const domain = origin.DomainName;
        // Match bucket.s3.amazonaws.com
        for (const bucket of buckets) {
          const expectedDomain = `${bucket}.s3.amazonaws.com`;
          if (domain === expectedDomain) {
            bucketToDists[bucket].push(distId);
          }
        }
      }
    }

    console.log("\n🧾 Bucket-to-Distribution Map:\n");

    for (const [bucket, dists] of Object.entries(bucketToDists)) {
      if (dists.length > 0) {
        console.log(`✅ ${bucket} → ${dists.join(", ")}`);
      } else {
        console.log(`❌ ${bucket} → No CloudFront distribution`);
      }
    }

    return bucketToDists;
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration object
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update configuration
   * @param {Object} newConfig - New configuration values to merge
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

module.exports = AWSManager;
