const {
  CloudFrontClient,
  ListDistributionsCommand,
  CreateDistributionCommand,
} = require("@aws-sdk/client-cloudfront");
const {
  S3Client,
  ListBucketsCommand,
  CreateBucketCommand,
  DeleteBucketCommand,
  ListObjectsV2Command,
  DeleteObjectsCommand,
} = require("@aws-sdk/client-s3");

/**
 * AWS Manager class for handling S3 and CloudFront operations
 */
class AWSManager {
  constructor(config = {}) {
    this.config = {
      TOTAL_BUCKETS: config.TOTAL_BUCKETS || 5,
      BUCKET_PREFIX: config.BUCKET_PREFIX || 'test-bucket',
      REGION: config.REGION || 'us-east-1',
      ORIGIN_LIMIT: config.ORIGIN_LIMIT || 25,
      RANDOM_REGIONS: config.RANDOM_REGIONS || false,
      ...config
    };

    // Available AWS regions for S3
    this.availableRegions = [
      'us-east-1',      // N. Virginia
      'us-east-2',      // Ohio
      'us-west-1',      // N. California
      'us-west-2',      // Oregon
      'eu-west-1',      // Ireland
      'eu-west-2',      // London
      'eu-west-3',      // Paris
      'eu-central-1',   // Frankfurt
      'ap-southeast-1', // Singapore
      'ap-southeast-2', // Sydney
      'ap-northeast-1', // Tokyo
      'ap-northeast-2', // Seoul
      'ap-south-1',     // Mumbai
      'ca-central-1',   // Canada
      'sa-east-1'       // São Paulo
    ];

    this.credentials = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    };

    // Initialize AWS clients
    this.s3 = new S3Client({
      region: this.config.REGION,
      credentials: this.credentials
    });

    this.cloudfront = new CloudFrontClient({
      region: "us-east-1", // CloudFront is global, but use us-east-1
      credentials: this.credentials
    });
  }

  /**
   * Get a random region from available regions
   * @returns {string} Random AWS region
   */
  getRandomRegion() {
    const randomIndex = Math.floor(Math.random() * this.availableRegions.length);
    return this.availableRegions[randomIndex];
  }

  /**
   * Create multiple S3 buckets based on configuration
   * @param {boolean} useRandomRegions - Whether to use random regions for each bucket
   */
  async createBuckets(useRandomRegions = null) {
    const shouldUseRandomRegions = useRandomRegions !== null ? useRandomRegions : this.config.RANDOM_REGIONS;

    console.log("🚀 Creating buckets...");
    if (shouldUseRandomRegions) {
      console.log("🌍 Using random regions for each bucket");
    }

    const createdBuckets = [];

    for (let i = 1; i <= this.config.TOTAL_BUCKETS; i++) {
      const bucketName = `${this.config.BUCKET_PREFIX}-${i.toString().padStart(3, "0")}`;
      const region = shouldUseRandomRegions ? this.getRandomRegion() : this.config.REGION;

      try {
        // Create S3 client for the specific region
        const s3Client = new S3Client({
          region: region,
          credentials: this.credentials
        });

        const createBucketParams = {
          Bucket: bucketName,
          ...(region !== "us-east-1"
            ? { CreateBucketConfiguration: { LocationConstraint: region } }
            : {})
        };

        await s3Client.send(new CreateBucketCommand(createBucketParams));

        console.log(`✅ Created bucket: ${bucketName} in region: ${region}`);
        createdBuckets.push({ name: bucketName, region: region });

      } catch (err) {
        if (err.name === "BucketAlreadyOwnedByYou") {
          console.log(`⚠️  Bucket already exists: ${bucketName} (region: ${region})`);
          createdBuckets.push({ name: bucketName, region: region, existed: true });
        } else {
          console.error(`❌ Error creating ${bucketName} in ${region}: ${err.message}`);
        }
      }
    }

    console.log(`\n📊 Bucket Creation Summary:`);
    console.log(`Total buckets processed: ${createdBuckets.length}`);

    if (shouldUseRandomRegions) {
      console.log(`\n🌍 Regions used:`);
      const regionCounts = {};
      createdBuckets.forEach(bucket => {
        regionCounts[bucket.region] = (regionCounts[bucket.region] || 0) + 1;
      });

      Object.entries(regionCounts).forEach(([region, count]) => {
        console.log(`   ${region}: ${count} bucket(s)`);
      });
    }

    return createdBuckets;
  }

  /**
   * Create CloudFront distribution with S3 origins
   * @param {Array} bucketNames - Optional array of bucket names, if not provided will generate from config
   */
  async createCloudFrontDistribution(bucketNames = null) {
    console.log("🚀 Creating CloudFront distribution...");
    
    if (!bucketNames) {
      bucketNames = Array.from({ length: this.config.TOTAL_BUCKETS }, (_, i) =>
        `${this.config.BUCKET_PREFIX}-${(i + 1).toString().padStart(3, "0")}`
      );
    }

    const origins = bucketNames.slice(0, this.config.ORIGIN_LIMIT).map((bucket, index) => ({
      Id: `Origin${index + 1}`,
      DomainName: `${bucket}.s3.amazonaws.com`,
      S3OriginConfig: {
        OriginAccessIdentity: ""
      }
    }));

    const distributionParams = {
      DistributionConfig: {
        CallerReference: `${Date.now()}`,
        Comment: "Test distribution with multiple S3 origins",
        Enabled: true,
        Origins: {
          Quantity: origins.length,
          Items: origins
        },
        DefaultCacheBehavior: {
          TargetOriginId: "Origin1",
          ViewerProtocolPolicy: "redirect-to-https",
          ForwardedValues: {
            QueryString: false,
            Cookies: { Forward: "none" }
          },
          TrustedSigners: {
            Enabled: false,
            Quantity: 0
          },
          MinTTL: 0
        }
      }
    };

    try {
      const result = await this.cloudfront.send(
        new CreateDistributionCommand(distributionParams)
      );
      console.log("✅ CloudFront Distribution ID:", result.Distribution.Id);
      console.log("🌍 Domain Name:", result.Distribution.DomainName);
      return result.Distribution;
    } catch (err) {
      console.error("❌ Failed to create CloudFront distribution:", err.message);
      throw err;
    }
  }

  /**
   * List all S3 buckets
   * @returns {Array} Array of bucket names
   */
  async listAllBuckets() {
    try {
      const res = await this.s3.send(new ListBucketsCommand({}));
      return res.Buckets.map(b => b.Name);
    } catch (err) {
      console.error("❌ Failed to list buckets:", err.message);
      return [];
    }
  }

  /**
   * Get all CloudFront distributions
   * @returns {Array} Array of distribution objects
   */
  async getAllDistributions() {
    const all = [];
    let Marker;

    try {
      do {
        const res = await this.cloudfront.send(new ListDistributionsCommand({ Marker }));
        const dists = res.DistributionList?.Items || [];
        all.push(...dists);
        Marker = res.DistributionList?.NextMarker;
      } while (Marker);
    } catch (err) {
      console.error("❌ Failed to list distributions:", err.message);
    }

    return all;
  }

  /**
   * Map S3 buckets to their associated CloudFront distributions
   */
  async mapBucketsToDistributions() {
    const buckets = await this.listAllBuckets();
    const distributions = await this.getAllDistributions();

    const bucketToDists = {}; // { bucketName: [distId, ...] }

    for (const bucket of buckets) {
      bucketToDists[bucket] = [];
    }

    for (const dist of distributions) {
      const distId = dist.Id;
      const origins = dist.Origins?.Items || [];

      for (const origin of origins) {
        const domain = origin.DomainName;
        // Match bucket.s3.amazonaws.com
        for (const bucket of buckets) {
          const expectedDomain = `${bucket}.s3.amazonaws.com`;
          if (domain === expectedDomain) {
            bucketToDists[bucket].push(distId);
          }
        }
      }
    }

    console.log("\n🧾 Bucket-to-Distribution Map:\n");

    for (const [bucket, dists] of Object.entries(bucketToDists)) {
      if (dists.length > 0) {
        console.log(`✅ ${bucket} → ${dists.join(", ")}`);
      } else {
        console.log(`❌ ${bucket} → No CloudFront distribution`);
      }
    }

    return bucketToDists;
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration object
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update configuration
   * @param {Object} newConfig - New configuration values to merge
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get list of available AWS regions
   * @returns {Array} Array of available region strings
   */
  getAvailableRegions() {
    return [...this.availableRegions];
  }

  /**
   * Display available regions with descriptions
   */
  showAvailableRegions() {
    const regionDescriptions = {
      'us-east-1': 'US East (N. Virginia)',
      'us-east-2': 'US East (Ohio)',
      'us-west-1': 'US West (N. California)',
      'us-west-2': 'US West (Oregon)',
      'eu-west-1': 'Europe (Ireland)',
      'eu-west-2': 'Europe (London)',
      'eu-west-3': 'Europe (Paris)',
      'eu-central-1': 'Europe (Frankfurt)',
      'ap-southeast-1': 'Asia Pacific (Singapore)',
      'ap-southeast-2': 'Asia Pacific (Sydney)',
      'ap-northeast-1': 'Asia Pacific (Tokyo)',
      'ap-northeast-2': 'Asia Pacific (Seoul)',
      'ap-south-1': 'Asia Pacific (Mumbai)',
      'ca-central-1': 'Canada (Central)',
      'sa-east-1': 'South America (São Paulo)'
    };

    console.log('🌍 Available AWS Regions:');
    this.availableRegions.forEach(region => {
      console.log(`   ${region} - ${regionDescriptions[region] || 'Unknown'}`);
    });
  }

  /**
   * Delete all objects in a bucket
   * @param {string} bucketName - Name of the bucket to empty
   */
  async emptyBucket(bucketName) {
    try {
      let continuationToken;

      do {
        const listParams = {
          Bucket: bucketName,
          ContinuationToken: continuationToken
        };

        const listResult = await this.s3.send(new ListObjectsV2Command(listParams));

        if (listResult.Contents && listResult.Contents.length > 0) {
          const deleteParams = {
            Bucket: bucketName,
            Delete: {
              Objects: listResult.Contents.map(obj => ({ Key: obj.Key }))
            }
          };

          await this.s3.send(new DeleteObjectsCommand(deleteParams));
          console.log(`🗑️  Deleted ${listResult.Contents.length} objects from ${bucketName}`);
        }

        continuationToken = listResult.NextContinuationToken;
      } while (continuationToken);

    } catch (err) {
      console.error(`❌ Error emptying bucket ${bucketName}: ${err.message}`);
      throw err;
    }
  }

  /**
   * Delete a single bucket (must be empty)
   * @param {string} bucketName - Name of the bucket to delete
   * @param {boolean} forceEmpty - Whether to empty the bucket first (default: true)
   */
  async deleteBucket(bucketName, forceEmpty = true) {
    try {
      if (forceEmpty) {
        await this.emptyBucket(bucketName);
      }

      await this.s3.send(new DeleteBucketCommand({ Bucket: bucketName }));
      console.log(`✅ Deleted bucket: ${bucketName}`);
      return true;
    } catch (err) {
      console.error(`❌ Error deleting bucket ${bucketName}: ${err.message}`);
      return false;
    }
  }

  /**
   * Delete all buckets that contain a specific string in their name
   * @param {string} searchString - String to search for in bucket names
   * @param {boolean} forceEmpty - Whether to empty buckets before deletion (default: true)
   * @param {boolean} dryRun - If true, only show what would be deleted without actually deleting (default: false)
   */
  async deleteBucketsContaining(searchString, forceEmpty = true, dryRun = false) {
    if (!searchString || searchString.trim() === '') {
      throw new Error('Search string cannot be empty');
    }

    console.log(`🔍 Searching for buckets containing: "${searchString}"`);

    const allBuckets = await this.listAllBuckets();
    const matchingBuckets = allBuckets.filter(bucket =>
      bucket.toLowerCase().includes(searchString.toLowerCase())
    );

    if (matchingBuckets.length === 0) {
      console.log(`ℹ️  No buckets found containing "${searchString}"`);
      return { deleted: [], failed: [] };
    }

    console.log(`📋 Found ${matchingBuckets.length} bucket(s) containing "${searchString}":`);
    matchingBuckets.forEach(bucket => console.log(`   - ${bucket}`));

    if (dryRun) {
      console.log(`🔍 DRY RUN: Would delete ${matchingBuckets.length} bucket(s)`);
      return { deleted: [], failed: [], dryRun: matchingBuckets };
    }

    console.log(`🚀 Starting deletion of ${matchingBuckets.length} bucket(s)...`);

    const deleted = [];
    const failed = [];

    for (const bucket of matchingBuckets) {
      const success = await this.deleteBucket(bucket, forceEmpty);
      if (success) {
        deleted.push(bucket);
      } else {
        failed.push(bucket);
      }
    }

    console.log(`\n📊 Deletion Summary:`);
    console.log(`✅ Successfully deleted: ${deleted.length} bucket(s)`);
    console.log(`❌ Failed to delete: ${failed.length} bucket(s)`);

    if (deleted.length > 0) {
      console.log(`\nDeleted buckets:`);
      deleted.forEach(bucket => console.log(`   ✅ ${bucket}`));
    }

    if (failed.length > 0) {
      console.log(`\nFailed deletions:`);
      failed.forEach(bucket => console.log(`   ❌ ${bucket}`));
    }

    return { deleted, failed };
  }
}

module.exports = AWSManager;
