require("dotenv").config();
const AWSManager = require('./aws-manager');

// Configuration for creating 10 buckets with random regions
const CONFIG = {
  TOTAL_BUCKETS: 10,
  BUCKET_PREFIX: 'random-region-bucket',
  REGION: 'us-east-1',
  ORIGIN_LIMIT: 25,
  RANDOM_REGIONS: true
};

// Initialize AWS Manager
const awsManager = new AWSManager(CONFIG);

/**
 * <PERSON><PERSON> script to create 10 buckets with random regions
 */
async function createBucketsDemo() {
  try {
    console.log('🌍 AWS S3 Bucket Creation with Random Regions Demo\n');

    // Show available regions
    console.log('📋 Available AWS Regions:');
    awsManager.showAvailableRegions();
    console.log('\n' + '='.repeat(60) + '\n');

    // Create buckets with random regions
    console.log(`🚀 Creating ${CONFIG.TOTAL_BUCKETS} buckets with random regions...\n`);
    
    const createdBuckets = await awsManager.createBuckets(true);
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ Bucket creation process completed!');
    
    // Show summary
    if (createdBuckets.length > 0) {
      console.log('\n📊 Final Summary:');
      createdBuckets.forEach((bucket, index) => {
        const status = bucket.existed ? '(already existed)' : '(newly created)';
        console.log(`   ${index + 1}. ${bucket.name} → ${bucket.region} ${status}`);
      });
    }

  } catch (error) {
    console.error('❌ Error during bucket creation:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

/**
 * Alternative demo with specific configuration
 */
async function customBucketDemo() {
  try {
    console.log('\n' + '='.repeat(60));
    console.log('🔧 Custom Configuration Demo\n');

    // Create a custom configuration
    const customConfig = {
      TOTAL_BUCKETS: 3,
      BUCKET_PREFIX: 'custom-test',
      RANDOM_REGIONS: true
    };

    const customManager = new AWSManager(customConfig);
    
    console.log('📋 Custom Configuration:');
    console.log(`   Total Buckets: ${customConfig.TOTAL_BUCKETS}`);
    console.log(`   Bucket Prefix: ${customConfig.BUCKET_PREFIX}`);
    console.log(`   Random Regions: ${customConfig.RANDOM_REGIONS}\n`);

    // Uncomment to create custom buckets
    // await customManager.createBuckets();

    console.log('💡 Uncomment the line above to create custom buckets');

  } catch (error) {
    console.error('❌ Error in custom demo:', error.message);
  }
}

// Run the demonstrations
(async () => {
  await createBucketsDemo();
  await customBucketDemo();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 Demo completed! Check your AWS console to see the created buckets.');
  console.log('💡 Remember to clean up buckets when done to avoid charges.');
})();
